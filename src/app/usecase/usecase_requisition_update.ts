import { Usecase } from "../../framework/core.js";
import { Approval, ApprovalGroup, DeleteApprovalGroups, FindApprovalGroup, SaveApprovalGroups, getApprovalList } from "../model/model_approval.js";
import { ApprovalTemplateRule, FindApprovalTemplateGroup, requisitionDocRuleValue, getRequistionDocRule } from "../model/model_approval_template.js";
import { FindDelegation } from "../model/model_delegation.js";
import { isOperationalServiceDepartment } from "../model/model_department.js";
import { Position } from "../model/model_position.js";
import { FindRequisition, Requisition, RequisitionPayload, SaveRequisition, validateRequisitionRequest } from "../model/model_requisition.js";
import { FindUser, FindUserSupervisors, User, UserLogin } from "../model/model_user.js";
import { ApprovalStatus, SubDocumentRequisition, TenderMethod, TypeOf, UserRole } from "../model/vo.js";

class Gateways {
  findRequisition: FindRequisition;
  findUser: FindUser;
  findUserSupervisors: FindUserSupervisors;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  saveRequisition: SaveRequisition;
  saveApprovalGroups: SaveApprovalGroups;
  deleteApprovalGroups: DeleteApprovalGroups;
  findDelegation: FindDelegation;
  findApprovalGroup: FindApprovalGroup;
}

export class Request {
  userLogin: UserLogin;
  requisitionId: string;
  requisition: RequisitionPayload & {
    requestForId: string;
    requesterBackToBackIds: string[];
    membersInvolvedIds: string[];
    departmentCode: string;
    sectionId: string;
    procPlanCurrency: string;
    procPlanValue: number;
    tenderMethod: TypeOf<typeof TenderMethod>;
  };
}

export class Response { }

export const requisitionUpdate: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const [rqs] = await o.findRequisition(ctx, { id: req.requisitionId });
    const rq = rqs.length > 0 ? rqs[0] : null;

    if (!rq) {
      throw new Error("requisition not found");
    }

    if (rq.status !== "DRAFT") {
      throw new Error("requisition must in DRAFT state");
    }

    const [requestForUsers] = await o.findUser(ctx, {
      ids: [req.requisition.requestForId],
      size: 1,
    });
    const requestForUser = requestForUsers.length > 0 ? requestForUsers[0] : null;
    if (!requestForUser) {
      throw new Error(`request for id ${req.requisition.requestForId} not found`);
    }

    let rbtbs: User[] = [];
    if (req.requisition.requesterBackToBackIds.length > 0) {
      [rbtbs] = await o.findUser(ctx, {
        ids: req.requisition.requesterBackToBackIds.filter((x, i, a) => a.indexOf(x) == i), // find unique only
      });
      if (rbtbs.length === 0) {
        throw new Error(`requester back to back for ids ${req.requisition.requesterBackToBackIds} not found`);
      }
    }

    let memberInvolveds: User[] = [];
    if (req.requisition.membersInvolvedIds.length > 0) {
      [memberInvolveds] = await o.findUser(ctx, {
        ids: req.requisition.membersInvolvedIds.filter((x, i, a) => a.indexOf(x) == i), // find unique only
      });
      if (memberInvolveds.length === 0) {
        throw new Error(`member involved for ids ${req.requisition.membersInvolvedIds} not found`);
      }
    }

    if (req.requisition.checkValidation) {
      validateRequisitionRequest(req.requisition);
    }

    let OEValue = req.requisition.value;

    if (req.requisition.currency === "IDR") {
      OEValue = OEValue / 10_000;
    }

    let procPlanValue = req.requisition.procPlanValue!;

    if (req.requisition.procPlanCurrency === "IDR") {
      procPlanValue = procPlanValue / 10_000;
    }

    //
    const [listUser] = await o.findUserSupervisors(ctx, { positionId: requestForUser.position?.id! });
    if (listUser.length === 0) {
      throw new Error(`user supervisor for Requisition is not found`);
    }

    rq.approvalGroup = null;

    await o.saveRequisition(ctx, rq);

    const description = `${req.userLogin.department?.name}-${req.requisition.commodity}-${req.requisition.title}`;

    const { minRQValue, maxRQValue, minOEValue, maxOEValue } = requisitionDocRuleValue;
    const docRule: ApprovalTemplateRule = getRequistionDocRule(
      req.requisition.commodity,
      req.requisition.departmentCode,
      req.requisition.budgetOwners,
      req.requisition.isAdditionalProcPlan,
      req.requisition.tenderMethod,
      req.requisition.contractTypeEngagement,
      OEValue,
      procPlanValue,
    );

    let listUserBudgetOwners: User[] = [];

    if (docRule.additionalBudgetOwnerManager && req.requisition.budgetOwners.length > 0) {
      for (const budgetOwner of req.requisition.budgetOwners) {
        const [budgetOwnerUsers] = await o.findUser(ctx, { departmentId: budgetOwner });
        const budgetOwnerUser = budgetOwnerUsers.filter((user) => user.position?.role === "MGR" && user.department?.id !== req.userLogin.department?.id);
        if (budgetOwnerUser.length > 0) {
          listUserBudgetOwners.push(...budgetOwnerUser);
        }
      }
    }

    const items = await getApprovalList(ctx, description, o.findUser, o.findApprovalTemplateGroup, o.findDelegation, listUser, rq.id!, "REQUISITION", rq.year, docRule, listUserBudgetOwners);

    // reconstruct approval list
    {
      const newApprovalGroup: ApprovalGroup[] = [];

      for (const item of items) {
        const newApprovals: Approval[] = [];
        for (const app of item.approvals) {
          if (UserRole.some((x) => x === app.currentUserInPosition?.position?.role)) {
            newApprovals.push({
              as: app.as,
              date: app.date,
              signer: app.signer ?? null,
              status: app.status,
              users: app.users ? app.users : [],
              position: app.position ? app.position : null,
              currentUserInPosition: app.currentUserInPosition ? app.currentUserInPosition : null,
            });
          } else {
            newApprovals.push(app);
          }
        }
        newApprovalGroup.push({
          ...item,
          approvals: newApprovals,
          sequence: item.sequence,
        });
      }

      const filteredByRuleResult: ApprovalGroup[] = newApprovalGroup.map((header) => ({
        ...header,
        approvals: header.approvals
          .filter((app) => !(app.subDocumentType === "INSURANCE_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(app.subDocumentType === "HSE_RISK_ASSESSMENT" && ["GM", "SMVP"].includes(app.currentUserInPosition?.position?.role ?? "")))
          .filter((app) => !(app.subDocumentType === "DA_JUSTIFICATION" && !!app.currentUserInPosition && !docRule.isDaJustification))
          .filter(
            (app) =>
              !(
                app.subDocumentType === "OWNER_ESTIMATION" &&
                ((app.currentUserInPosition?.position?.role === "GM" && OEValue <= maxOEValue) ||
                  (app.currentUserInPosition?.position?.role === "SMVP" && OEValue <= minOEValue))
              )
          )
          .filter(
            (app) =>
              !(
                app.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                app.currentUserInPosition?.position?.role === "GM" &&
                OEValue <= maxRQValue
              )
          )
          .filter(
            (app) =>
              !(
                app.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                (app.currentUserInPosition?.position?.role === "SMVP" || app.currentUserInPosition?.position?.role === "GM") &&
                OEValue <= minRQValue
              )
          )
          .filter(
            (app, index) =>
              !(
                app.subDocumentType === "REQUISITION" &&
                !docRule.isAdditionalProcPlanOrValueMoreThanPercentage &&
                index !== 0 &&
                app.currentUserInPosition &&
                req.requisition.commodity === "GOODS" &&
                req.requisition.contractTypeEngagement === "FIRM_COMMITMENT"
              )
          ).filter((app) => 
            !(
              app.subDocumentType !== "OWNER_ESTIMATION" && 
              app.currentUserInPosition?.position?.role === "MGR" && 
              req.requisition.budgetOwners.length > 0 &&
              listUserBudgetOwners.some((user) => user.id === app.currentUserInPosition?.id)
            )
          ),
      }));

      await o.deleteApprovalGroups(ctx, { documentId: rq.id, documentType: "REQUISITION" });

      await o.saveApprovalGroups(ctx, filteredByRuleResult);
    }

    rq.approvalGroup = items[0];

    const updatedRq: Requisition = {
      ...rq,
      ...req.requisition,
      requestFor: requestForUser,
      requesterBackToBack: rbtbs,
      membersInvolved: memberInvolveds,
    };

    await o.saveRequisition(ctx, updatedRq);

    return {};
  },
};

type ApprovalHeader = {
  subDocumentType: TypeOf<typeof SubDocumentRequisition> | null;
  approvals: ApprovalDetail[];
};

type ApprovalDetail = {
  as?: string | null;
  date?: Date | null;
  signer?: string | null;
  status?: TypeOf<typeof ApprovalStatus>;
  users?: User[];
  position?: Position | null;
  currentUserInPosition?: User | null;
  sequence?: number;
};

// export interface ApprovalGroup {
//   id?: string;
//   documentId?: string;
//   documentType?: TypeOf<typeof DocumentTemplate>;
//   sequence?: number;
//   approvals: Approval[];
//   status?: TypeOf<typeof ApprovalStatus>;
//   description?: string;
//   nextApprovalGroupId?: string | null;
// }
