import { Usecase } from "../../framework/core.js";
import { approvalFlow } from "./usecase_approval_flow.js";
import { approvalGetAll } from "./usecase_approval_getall.js";
import { approvalProcessing } from "./usecase_approval_processing.js";
import { approvalTemplateCreate } from "./usecase_approvaltemplate_create.js";
import { approvalTemplateGetAll } from "./usecase_approvaltemplate_getall.js";
import { departmentGetAll } from "./usecase_department_getall.js";
import { documentHistoryGetAll } from "./usecase_document_history_getall.js";
import { loginSimple } from "./usecase_login_simple.js";
import { positionGetAll } from "./usecase_position_getall.js";
import { prItemGetAll } from "./usecase_pr_item_getall.js";
import { pqClarificationApprove } from "./usecase_pq_clarification_approve.js";
import { pqClarificationGetOne } from "./usecase_pq_clarification_getone.js";
import { pqClarificationSendback } from "./usecase_pq_clarification_sendback.js";
import { pqClarificationUpdate } from "./usecase_pq_clarification_update.js";
import { pqDocument } from "./usecase_pq_document.js";
import { pqEvaluationApprove } from "./usecase_pq_evaluation_approve.js";
import { pqEvaluationGetOne } from "./usecase_pq_evaluation_getone.js";
import { pqEvaluationSendback } from "./usecase_pq_evaluation_sendback.js";
import { pqEvaluationUpdate } from "./usecase_pq_evaluation_update.js";
import { pqEvaluationSubmit } from "./usecase_pq_evaluation_submit.js";
import { pqEvaluationVendorSave } from "./usecase_pq_evaluation_vendor_save.js";
import { pqEvaluationVendorSubmit } from "./usecase_pq_evaluation_vendor_submit.js";
import { pqGetAll } from "./usecase_pq_getall.js";
import { pqMeetingGetOne } from "./usecase_pq_meeting_getone.js";
import { pqMeetingUpdate } from "./usecase_pq_meeting_update.js";
import { pqMeetingSubmit } from "./usecase_pq_meeting_submit.js";
import { pqRegistrationApprove } from "./usecase_pq_registration_approve.js";
import { pqRegistrationGetOne } from "./usecase_pq_registration_getone.js";
import { pqRegistrationSendback } from "./usecase_pq_registration_sendback.js";
import { pqRegistrationUpdate } from "./usecase_pq_registration_update.js";
import { pqRequirementApprove } from "./usecase_pq_requirement_approve.js";
import { pqRequirementGetOne } from "./usecase_pq_requirement_getone.js";
import { pqRequirementSendback } from "./usecase_pq_requirement_sendback.js";
import { pqRequirementSubmit } from "./usecase_pq_requirement_submit.js";
import { pqRequirementUpdate } from "./usecase_pq_requirement_update.js";
import { pqVendorGetAll } from "./usecase_pq_vendor_getall.js";
import { procplanAppActionApprove } from "./usecase_procplan_app_action_approve.js";
import { procplanAppActionSendback } from "./usecase_procplan_app_action_sendback.js";
import { procplanAppActionSendbackToUpp } from "./usecase_procplan_app_action_sendback_to_upp.js";
import { procplanAppActionSubmit } from "./usecase_procplan_app_action_submit.js";
import { procplanAppDetailCreate } from "./usecase_procplan_app_detail_create.js";
import { procplanAppDetailDelete } from "./usecase_procplan_app_detail_delete.js";
import { procplanAppDetailGetOne } from "./usecase_procplan_app_detail_getone.js";
import { procplanAppDetailUpdate } from "./usecase_procplan_app_detail_update.js";
import { procplanAppHeaderGetAll } from "./usecase_procplan_app_header_getall.js";
import { procplanAppHeaderGetOne } from "./usecase_procplan_app_header_getone.js";
import { procplanAppHeaderGetOne2 } from "./usecase_procplan_app_header_getone2.js";
import { procplanGetAll } from "./usecase_procplan_getall.js";
import { procplanUppActionApprove } from "./usecase_procplan_upp_action_approve.js";
import { procplanUppActionSendback } from "./usecase_procplan_upp_action_sendback.js";
import { procplanUppActionSubmit } from "./usecase_procplan_upp_action_submit.js";
import { procplanUppDetailCreate } from "./usecase_procplan_upp_detail_create.js";
import { procplanUppDetailDelete } from "./usecase_procplan_upp_detail_delete.js";
import { procplanUppDetailGetOne } from "./usecase_procplan_upp_detail_getone.js";
import { procplanUppDetailGetOne2 } from "./usecase_procplan_upp_detail_getone2.js";
import { procplanUppDetailUpdate } from "./usecase_procplan_upp_detail_update.js";
import { procplanUppHeaderGetAll } from "./usecase_procplan_upp_header_getall.js";
import { procplanUppHeaderGetOne } from "./usecase_procplan_upp_header_getone.js";
import { procplanUppHeaderGetOne2 } from "./usecase_procplan_upp_header_getone2.js";
import { procplanUppHeaderGetOne3 } from "./usecase_procplan_upp_header_getone3.js";
import { procplanUppHeaderGetOne4 } from "./usecase_procplan_upp_header_getone4.js";
import { requisitionActionApprove } from "./usecase_requisition_action_approve.js";
import { requisitionActionAssign } from "./usecase_requisition_action_assign.js";
import { requisitionActionSendback } from "./usecase_requisition_action_sendback.js";
import { requisitionActionSubmit } from "./usecase_requisition_action_submit.js";
import { requisitionCreate } from "./usecase_requisition_create.js";
import { requisitionDelete } from "./usecase_requisition_delete.js";
import { requisitionGetAll } from "./usecase_requisition_getall.js";
import { requisitionGetAllRbtb } from "./usecase_requisition_getallrbtb.js";
import { requisitionGetOne } from "./usecase_requisition_getone.js";
import { requisitionUpdate } from "./usecase_requisition_update.js";
import { requisitionUserPIC } from "./usecase_requisition_user_pic.js";
import { requisitionInsuranceUpdate } from "./usecase_requisition_insurance_update.js";
import { requisitionExternalDepartmentGetAll } from "./usecase_requisition_externaldepartment_getall.js";
import { sectionGetAll } from "./usecase_section_getall.js";
import { typeworkGetAll } from "./usecase_typework_getall.js";
import { userGetAll } from "./usecase_user_getall.js";
import { vendorGetAll } from "./usecase_vendor_getall.js";
import { vendorGetOne } from "./usecase_vendor_getone.js";
import { vendorPQClarificationGetOne } from "./usecase_vendor_pq_clarification_getone.js";
import { vendorPQClarificationSubmit } from "./usecase_vendor_pq_clarification_submit.js";

import { vendorPQEvaluationGetOne } from "./usecase_vendor_pq_evaluation_getone.js";
import { vendorPQEvaluationSubmit } from "./usecase_vendor_pq_evaluation_submit.js";
import { vendorPQGetAll } from "./usecase_vendor_pq_getall.js";
import { vendorVendorGetAll } from "./usecase_vendor_vendor_getall.js";
import { loginVendor } from "./usecase_vendor_pq_login.js";
import { vendorPQRegistrationGetOne } from "./usecase_vendor_pq_registration_getone.js";
import { vendorPQRegistrationSubmit } from "./usecase_vendor_pq_registration_submit.js";
import { vendorPQRegistrationUpdate } from "./usecase_vendor_pq_registration_update.js";
import { vendorPQRequirementGetOne } from "./usecase_vendor_pq_requirement_getone.js";
import { vendorPQRequirementSubmit } from "./usecase_vendor_pq_requirement_submit.js";
import { vendorPQMeetingGetOne } from "./usecase_vendor_pq_meeting_getone.js";
import { vendorPQSubmissionGetOne } from "./usecase_vendor_pq_submission_getone.js";
import { vendorPQSubmissionSubmit } from "./usecase_vendor_pq_submission_submit.js";
import { vendorPQSubmissionSave } from "./usecase_vendor_pq_submission_save.js";
import { pqGetOne } from "./usecase_pq_getone.js";
import { vendorPQGetOne } from "./usecase_vendor_pq_getone.js";
import { vendorPQTemplateGetOne } from "./usecase_vendor_pq_template_getone.js";
import { pqRegistrationComplete } from "./usecase_pq_registration_complete.js";
import { mailTest } from "./usecase_mail_test.js";
import { settingValuesGet } from "./usecase_settingvalues_get.js";
import { settingValuesSave } from "./usecase_settingvalues_save.js";
import { userEmailUpdate } from "./usecase_user_email_update.js";
import { vendorCivdGetAll } from "./usecase_vendor_civd_getall.js";
import { vendorCivdGetOne } from "./usecase_vendor_civd_getone.js";
import { loginVendorVera } from "./usecase_vendor_vera_login.js";
import { dashboardApproval } from "./usecase_dashboard_approval.js";
import { dashboardProcplan } from "./usecase_dashboard_procplan.js";
import { dashboardRequisition } from "./usecase_dashboard_requisition.js";
import { departmentGetAllByUser } from "./usecase_department_getall_byuser.js";
import { businessFieldLicenseGetAll } from "./usecase_business_field_license_getall.js";
import { businessFieldLicenseDocumentGetAll } from "./usecase_business_field_license_document_getall.js";
import { requisitionHseRiskUpdate } from "./usecase_requisition_hse_risk_update.js";
import { requisitionAdminUpdate } from "./usecase_requisition_admin_update.js";
import { approvalAdminRevert } from "./usecase_approval_admin_revert.js";
import { approvalResendEmail } from "./usecase_approval_resend_email.js";
import { approvalAdminApprove } from "./usecase_approval_admin_approve.js";
import { delegationCreate } from "./usecase_delegation_create.js";

class Gateways { }

export class Request { }

export class Response {
  message: string;
}

export const emptyUsecase: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    return { message: "test" };
  },
};

export const usecases: Record<string, Usecase> = {
  //
  dashboardApproval,
  dashboardProcplan,
  dashboardRequisition,

  approvalFlow,
  approvalGetAll,
  documentHistoryGetAll,
  approvalProcessing,
  approvalAdminRevert,
  approvalResendEmail,
  approvalAdminApprove,

  approvalTemplateCreate,
  approvalTemplateGetAll,

  departmentGetAll,
  departmentGetAllByUser,

  positionGetAll,

  prItemGetAll,

  procplanAppActionApprove,
  procplanAppActionSendback,
  procplanAppActionSendbackToUpp,
  procplanAppActionSubmit,
  procplanAppDetailCreate,
  procplanAppDetailDelete,
  procplanAppDetailGetOne,
  procplanUppDetailGetOne2,
  procplanAppDetailUpdate,
  procplanAppHeaderGetAll,
  procplanAppHeaderGetOne,
  procplanAppHeaderGetOne2,

  procplanGetAll,
  procplanUppActionApprove,
  procplanUppActionSendback,
  procplanUppActionSubmit,
  procplanUppDetailCreate,
  procplanUppDetailDelete,
  procplanUppDetailGetOne,
  procplanUppDetailUpdate,
  procplanUppHeaderGetAll,
  procplanUppHeaderGetOne,
  procplanUppHeaderGetOne2,
  procplanUppHeaderGetOne3,
  procplanUppHeaderGetOne4,

  requisitionActionApprove,
  requisitionActionAssign,
  requisitionActionSendback,
  requisitionActionSubmit,
  requisitionCreate,
  requisitionDelete,
  requisitionGetAll,
  requisitionGetAllRbtb,
  requisitionGetOne,
  requisitionUpdate,
  requisitionUserPIC,
  requisitionInsuranceUpdate,
  requisitionHseRiskUpdate,
  requisitionAdminUpdate,
  requisitionExternalDepartmentGetAll,

  sectionGetAll,
  typeworkGetAll,
  userEmailUpdate,
  userGetAll,
  vendorGetAll,
  vendorGetOne,
  emptyUsecase,
  loginSimple,

  vendorCivdGetAll,
  vendorCivdGetOne,

  pqVendorGetAll,
  pqDocument,
  pqGetAll,
  pqGetOne,

  pqRequirementGetOne,
  pqRequirementUpdate,
  pqRequirementSubmit,
  pqRequirementApprove,
  pqRequirementSendback,

  pqRegistrationComplete,
  pqRegistrationGetOne,
  pqRegistrationUpdate,
  pqRegistrationApprove,
  pqRegistrationSendback,

  pqMeetingGetOne,
  pqMeetingUpdate,
  pqMeetingSubmit,

  pqEvaluationGetOne,
  pqEvaluationUpdate,
  pqEvaluationApprove,
  pqEvaluationSendback,
  pqEvaluationSubmit,
  pqEvaluationVendorSave,
  pqEvaluationVendorSubmit,

  pqClarificationGetOne,
  pqClarificationUpdate,
  pqClarificationApprove,
  pqClarificationSendback,

  loginVendor,
  loginVendorVera,

  vendorPQGetAll,
  vendorPQGetOne,

  vendorPQTemplateGetOne,

  vendorPQRegistrationGetOne,
  vendorPQRegistrationSubmit,
  vendorPQRegistrationUpdate,

  vendorPQRequirementGetOne,
  vendorPQRequirementSubmit,

  vendorPQMeetingGetOne,

  vendorPQSubmissionGetOne,
  vendorPQSubmissionSave,
  vendorPQSubmissionSubmit,

  vendorPQEvaluationGetOne,
  vendorPQEvaluationSubmit,

  vendorPQClarificationGetOne,
  vendorPQClarificationSubmit,
  vendorVendorGetAll,
  mailTest,

  businessFieldLicenseGetAll,
  businessFieldLicenseDocumentGetAll,

  settingValuesGet,
  settingValuesSave,

  delegationCreate,
};
