import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, getApproval, moveApproval, validateApprovalAction } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, FindPQVendor, SavePQTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  findPQVendor: FindPQVendor;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  comment: string;
  index: "1" | "2" | "3";
}

export class Response { }

export const pqEvaluationApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "EVALUATION") {
      throw new Error("pq must in EVALUATION PHASE");
    }

    if (!pqTemplate.phasesEvaluation) {
      throw new Error("pq phase evaluation not found");
    }

    if (req.index === "1") {
      if (pqTemplate.phasesEvaluation.submission1?.approval?.status !== "ON_REVIEW") {
        throw new Error("pq must in ON_REVIEW state");
      }

      validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.submission1?.approval?.approvalGroup!);
      // TODO: update evaluationDate field on last approval

      const approval = getApproval(pqTemplate.phasesEvaluation.submission1?.approval?.approvalGroup!, req.userLogin);
      if (approval) {
        approval.date = getDateOnly(now);
        approval.signer = req.userLogin;
        approval.status = "DONE";
      }

      const paralelAND = pqTemplate.phasesEvaluation.submission1?.approval?.approvalGroup
        ? pqTemplate.phasesEvaluation.submission1?.approval?.approvalGroup.approvals.every((x) => x.status === "DONE")
        : false;

      const autoApprove = await moveApproval(ctx, paralelAND, pqTemplate.phasesEvaluation.submission1?.approval!, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

      if (autoApprove) {
        const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });
        for (const pqVendor of pqVendors) {
          pqVendor.phaseSubmission!.submission1!.evaluationDate = getDateOnly(now);
        }
      }

    } //
    else if (req.index === "2") {
      if (pqTemplate.phasesEvaluation.submission2?.approval?.status !== "ON_REVIEW") {
        throw new Error("pq must in ON_REVIEW state");
      }

      validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.submission2?.approval?.approvalGroup!);

      const approval = getApproval(pqTemplate.phasesEvaluation.submission2?.approval?.approvalGroup!, req.userLogin);
      if (approval) {
        approval.date = getDateOnly(now);
        approval.signer = req.userLogin;
        approval.status = "DONE";
      }

      const paralelAND = pqTemplate.phasesEvaluation.submission2?.approval?.approvalGroup
        ? pqTemplate.phasesEvaluation.submission2?.approval?.approvalGroup.approvals.every((x) => x.status === "DONE")
        : false;

      const autoApprove = await moveApproval(ctx, paralelAND, pqTemplate.phasesEvaluation.submission2?.approval!, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

      if (autoApprove) {
        const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });
        for (const pqVendor of pqVendors) {
          pqVendor.phaseSubmission!.submission2!.evaluationDate = getDateOnly(now);
        }
      }

    } //
    else if (req.index === "3") {
      if (pqTemplate.phasesEvaluation.submission3?.approval?.status !== "ON_REVIEW") {
        throw new Error("pq must in ON_REVIEW state");
      }

      validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.submission3?.approval?.approvalGroup!);

      const approval = getApproval(pqTemplate.phasesEvaluation.submission3?.approval?.approvalGroup!, req.userLogin);
      if (approval) {
        approval.date = getDateOnly(now);
        approval.signer = req.userLogin;
        approval.status = "DONE";
      }

      const paralelAND = pqTemplate.phasesEvaluation.submission3?.approval?.approvalGroup
        ? pqTemplate.phasesEvaluation.submission3?.approval?.approvalGroup.approvals.every((x) => x.status === "DONE")
        : false;

      const autoApprove = await moveApproval(ctx, paralelAND, pqTemplate.phasesEvaluation.submission3?.approval!, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

      if (autoApprove) {
        const [pqVendors] = await o.findPQVendor(ctx, { pqId: req.pqId });
        for (const pqVendor of pqVendors) {
          pqVendor.phaseSubmission!.submission3!.evaluationDate = getDateOnly(now);
        }
      }

    }

    await o.savePQTemplate(ctx, pqTemplate);

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>,
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Evaluation ${req.index} Approve`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
