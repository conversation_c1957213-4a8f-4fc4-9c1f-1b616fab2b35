import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, getApproval, moveApproval, validateApprovalAction } from "../model/model_approval.js";
import { FindCalendar } from "../model/model_calendar.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindProcPlanDetail, FindProcPlanHeader, SaveProcPlanDetail, SaveProcPlanHeader } from "../model/model_procplan.js";
import { FindRequisition, SaveRequisition } from "../model/model_requisition.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, formatNumber } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTemplateTableData, sendMail } from "../utility/mailer.js";

class Gateways {
  findProcPlanHeader: FindProcPlanHeader;
  findProcPlanDetail: FindProcPlanDetail;
  findApprovalGroup: FindApprovalGroup;
  findDelegation: FindDelegation;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveProcPlanDetail: SaveProcPlanDetail;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  findCalendar: FindCalendar;
  dateNow: DateNowHandler;
  findRequisition: FindRequisition;
  saveRequisition: SaveRequisition;
}

export class Request {
  userLogin: UserLogin;
  headerId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const procplanAppActionApprove: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //

    const now = await o.dateNow(ctx);

    const [pphs] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "APP" });
    let pph = pphs.length > 0 ? pphs[0] : null;

    // let pph = await getLastProcplanHeader(ctx, findProcPlanHeader, req.departmentId, null, req.year, "APP");

    if (!pph) {
      throw new Error("procplan header not found");
    }

    if (pph.status !== "ON_REVIEW") {
      throw new Error("proc plan must in ON_REVIEW state");
    }

    const [checkActing] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "ACTING" });
    const acting = checkActing.length > 0 ? checkActing[0] : null;
    
    validateApprovalAction(req.userLogin, pph.approvalGroup, null, acting);

    const approval = getApproval(pph.approvalGroup!, req.userLogin);
    if (approval) {
      approval.date = getDateOnly(now);
      approval.signer = req.userLogin;
      approval.status = "DONE";
    }

    const paralelAND = pph.approvalGroup ? pph.approvalGroup.approvals.every((x) => x.status === "DONE") : false;

    const autoApprove = await moveApproval(ctx, paralelAND, pph, o.findApprovalGroup, o.saveApprovalGroups, o.findCalendar);

    await o.saveProcPlanHeader(ctx, pph);

    if (autoApprove) {
      const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pph.id });

      let sequenceGoods: number = 1;
      let sequenceServices: number = 1;
      let sequenceOthers: number = 1;
      const [rqs] = await o.findRequisition(ctx, { isAdditionalProcPlan: false, year: pph.year! + 1, departmentId: pph.department?.id! });

      for (const ppd of ppds) {
        //

        // const cm = ppd.commodity === "GOODS" ? "1" : ppd.commodity === "SERVICES" ? "2" : "Y";
        const ct = ppd.createdType === "NEW_PROC_PLAN" ? "A" : ppd.createdType === "CARRY_OVER" ? "B" : "X";
        let cm = "Y"; // others
        let sequenceString = sequenceOthers.toString();

        if (ppd.commodity === "GOODS") {
          cm = "1";
          sequenceString = sequenceGoods.toString();
        } else if (ppd.commodity === "SERVICES") {
          cm = "2";
          sequenceString = sequenceServices.toString();
        }

        ppd.procPlanCode = `A075-${(ppd.year + 1).toString().substring(2)}-${ct}-${ppd.department?.code}${cm}${sequenceString.padStart(3, "0")}`;

        if (ppd.commodity === "GOODS") {
          sequenceGoods++;
        } else if (ppd.commodity === "SERVICES") {
          sequenceServices++;
        } else {
          sequenceOthers++;
        }

        // update requisition procPlanDetail info
        const filteredRqs = rqs.filter((rq) => rq.procPlanDetailId === ppd.id || rq.procPlanDetails?.some((detail) => detail.value === ppd.id));

        if (filteredRqs.length > 0) {
          for (const rq of filteredRqs) {
            if (rq.procPlanDetailId === ppd.id) {
              rq.procPlanCurrency = ppd.currency;
              rq.procPlanValue = ppd.valueEstimation;
              rq.procPlanDetailCode = ppd.procPlanCode;
            }
            
            if (rq.procPlanDetails && rq.procPlanDetails.length > 0) {
              const procPlanDetail = rq.procPlanDetails.find((detail) => detail.value === ppd.id);
              if (procPlanDetail) {
                procPlanDetail.label = ppd.procPlanCode;
              }
            }

            // Save updated requisitions
            await o.saveRequisition(ctx, rq);
          }

        }

      }

      await o.saveProcPlanDetail(ctx, ppds);
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pph.id!,
      documentType: "PROC_PLAN_APP",
      comment: req.comment,
      date: getDateOnly(now),
      message: `APP Approved`,
      user: req.userLogin,
      id: `${pph.id}-${formatDateWithSecond(now)}`,
    });

    const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: pph.id!, procPlanType: "APP" });

    if (pph.approvalGroup && pph.approvalGroup.sequence! > 1) {
      // set data for email reminder
      const sectionsName = pph.sections.map((section) => section.name).join(", <br/>");
      const approvalUser = pph.approvalGroup.approvals[0].currentUserInPosition ?? pph.approvalGroup.approvals[0].users![0];

      const ppdReminders: MailTemplateTableData[] = [{
        department: pph.department?.name!,
        section: sectionsName,
        quantity: ppds.length,
        value: pph.totalValueEstimation ? formatNumber(pph.totalValueEstimation.find((x) => x.currency === "USD")?.value! + (pph.totalValueEstimation.find((x) => x.currency === "IDR")?.value! / 10_000)) : 0, // USD
        url: "/procurement-plan/request/app?department=" + pph.department?.id,
      }];

      sendMail({
        sendToUserMail: approvalUser.email!,
        sendToUserName: approvalUser.name!,
        mailSubject: "PRISA - Approval for Procurement Plan",
        mailTitle: "Approval for Procurement Plan",
      }, ppdReminders, "APPROVAL");
    }

    // TODO create Procplan Code!!

    return {};
  },
};
