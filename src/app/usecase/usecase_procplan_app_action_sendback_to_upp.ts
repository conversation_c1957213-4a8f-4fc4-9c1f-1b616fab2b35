import { Usecase } from "../../framework/core.js";
import { DeleteApprovalGroups, FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { FindApprovalTemplateGroup } from "../model/model_approval_template.js";
import { FindDelegation } from "../model/model_delegation.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { DeleteProcPlanDetailByHeaderId, DeleteProcPlanHeader, FindProcPlanDetail, FindProcPlanHeader, SaveProcPlanHeader } from "../model/model_procplan.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, formatNumber } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";
import { MailTemplateTableData, sendMail } from "../utility/mailer.js";

class Gateways {
  findProcPlanHeader: FindProcPlanHeader;
  findApprovalGroup: FindApprovalGroup;
  findApprovalTemplateGroup: FindApprovalTemplateGroup;
  findProcPlanDetail: FindProcPlanDetail;
  findDelegation: FindDelegation;
  saveProcPlanHeader: SaveProcPlanHeader;
  saveDocumentHistory: SaveDocumentHistory;
  saveApprovalGroups: SaveApprovalGroups;
  deleteProcPlanDetailByHeaderId: DeleteProcPlanDetailByHeaderId;
  deleteProcPlanHeader: DeleteProcPlanHeader;
  deleteApprovalGroups: DeleteApprovalGroups;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  headerId: string;
  comment: string;
  // now: DateOrString;
}

export class Response { }

export const procplanAppActionSendbackToUpp: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    //
    const now = await o.dateNow(ctx);

    // find APP
    const [pphAPPs] = await o.findProcPlanHeader(ctx, { id: req.headerId, procPlanType: "APP" });
    let pphAPP = pphAPPs.length > 0 ? pphAPPs[0] : null;

    if (!pphAPP) {
      throw new Error("procplan APP header not found");
    }

    if (pphAPP.status !== "DRAFT") {
      throw new Error("proc plan must in DRAFT state");
    }

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    const [atg] = await o.findApprovalTemplateGroup(ctx, { documentType: "PROC_PLAN_APP" });
    const [checkActing] = await o.findDelegation(ctx, { delegateToUserId: req.userLogin.id, type: "ACTING" });
    const acting = checkActing.length > 0 ? checkActing[0] : null;

    validateApprovalAction(req.userLogin, pphAPP.approvalGroup, atg[0], acting);

    const [pphUPPs] = await o.findProcPlanHeader(ctx, { departmentIds: [pphAPP.department!.id], year: pphAPP.year, procPlanType: "UPP", status: "APPROVED" });
    if (!pphUPPs || pphUPPs.length === 0) {
      throw new Error("Procplan UPP not found");
    }

    // remove procplan APP detail
    await o.deleteProcPlanDetailByHeaderId(ctx, { procPlanHeader: { id: pphAPP.id } });

    // remove procplan APP header
    await o.deleteProcPlanHeader(ctx, { id: pphAPP.id });

    // remove approval with APP id
    await o.deleteApprovalGroups(ctx, { documentId: pphAPP.id, documentType: "PROC_PLAN_APP" });

    if (pphUPPs && pphUPPs.length > 0) {
      // find UPP
      // const [pphUPPs] = await o.findProcPlanHeader(ctx, { id: pphUPPId, procPlanType: "UPP" });
      // let pphUPP = pphUPPs.length > 0 ? pphUPPs[0] : null;

      for (const pphUPP of pphUPPs) {
        // send back to UPP & RESET ulang semua approval
        {
          //====
          const [approvals] = await o.findApprovalGroup(ctx, { documentId: pphUPP.id, documentType: "PROC_PLAN_UPP" });
          approvals.forEach((upps, i) => {
            upps.approvals.forEach((upp) => {
              upp.date = null;
              upp.signer = null;
              upp.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
            });
            upps.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
          });

          await o.saveApprovalGroups(ctx, approvals);

          pphUPP.status = "DRAFT";
          pphUPP.isSendBack = true;
          pphUPP.approvalGroup = approvals[0];

          await o.saveProcPlanHeader(ctx, pphUPP);
          //====
        }

        await o.saveDocumentHistory(ctx, {
          documentId: pphUPP.id!,
          documentType: "PROC_PLAN_UPP",
          comment: req.comment,
          date: getDateOnly(now),
          message: `APP to UPP Sent Back`,
          user: req.userLogin,
          id: `${pphUPP.id}-${formatDateWithSecond(now)}`,
        });

        // const [ppds] = await o.findProcPlanDetail(ctx, { procPlanHeaderId: req.headerId });

        let mailRecipients: { name: string, email: string }[] = []; // submitter

        mailRecipients.push({ name: pphUPP.submitter!.name!, email: pphUPP.submitter!.email! });

        const [approvals] = await o.findApprovalGroup(ctx, { documentId: pphUPP.id, documentType: "PROC_PLAN_UPP" });
        const requestForUser = approvals.find((appoval => appoval.sequence === 1));
        const requestForRecipient = requestForUser!.approvals[0].currentUserInPosition ?? requestForUser!.approvals[0].users![0];

        if (requestForRecipient.id !== pphUPP.submitter?.id) {
          mailRecipients.push({ name: requestForRecipient.name!, email: requestForRecipient.email! }); // requestfor
        }

        const ppdReminders: MailTemplateTableData[] = [{
          department: pphUPP.department?.name!,
          section: pphUPP.section?.name!,
          quantity: pphUPP.count,
          value: pphUPP.totalValueEstimation ? formatNumber(pphUPP.totalValueEstimation.find((x) => x.currency === "USD")?.value! + (pphUPP.totalValueEstimation.find((x) => x.currency === "IDR")?.value! / 10_000)) : 0, // USD
          url: "/procurement-plan/request/upp?section=" + pphUPP.section?.id,
        }];

        for (const mailRecipient of mailRecipients) {
          if (mailRecipient.email || mailRecipient.email !== "") {
            sendMail({
              sendToUserMail: mailRecipient.email!,
              sendToUserName: mailRecipient.name!,
              mailSubject: "PRISA - Procurement Plan Send Back",
              mailTitle: "Procurement Plan Send Back",
            }, ppdReminders, "SENDBACK");
          }
        }
      }
    }

    return {};
  },
};
