import { Usecase } from "../../framework/core.js";
import { FindApprovalGroup, SaveApprovalGroups, validateApprovalAction } from "../model/model_approval.js";
import { SaveDocumentHistory } from "../model/model_document_history.js";
import { FindOnePQTemplate, SavePQTemplate } from "../model/model_prequalification.js";
import { UserLogin } from "../model/model_user.js";
import { DateNowHandler, DocumentTemplate, TypeOf } from "../model/vo.js";
import { formatDateWithSecond, getDateOnly } from "../utility/helper.js";

class Gateways {
  findOnePQTemplate: FindOnePQTemplate;
  savePQTemplate: SavePQTemplate;
  findApprovalGroup: FindApprovalGroup;
  saveApprovalGroups: SaveApprovalGroups;
  saveDocumentHistory: SaveDocumentHistory;
  dateNow: DateNowHandler;
}

export class Request {
  userLogin: UserLogin;
  pqId: string;
  comment: string;
  index: "1" | "2" | "3";
}

export class Response { }

export const pqEvaluationSendback: Usecase<Gateways, Request, Response> = {
  gateways: Gateways,
  request: Request,
  setup: (o) => async (ctx, req) => {
    const pqTemplate = await o.findOnePQTemplate(ctx, req);
    const now = await o.dateNow(ctx);

    if (!pqTemplate) {
      throw new Error("pq not found");
    }

    if (pqTemplate.currentPhase !== "EVALUATION") {
      throw new Error("pq must in EVALUATION PHASE");
    }

    if (!pqTemplate.phasesEvaluation) {
      throw new Error("pq phase evaluation not found");
    }

    if (req.index === "1") {
      if (pqTemplate.phasesEvaluation.submission1?.approval?.status !== "ON_REVIEW") {
        throw new Error("pq must in ON_REVIEW state");
      }

      validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.submission1.approval.approvalGroup!);
    } //
    else if (req.index === "2") {
      if (pqTemplate.phasesEvaluation.submission2?.approval?.status !== "ON_REVIEW") {
        throw new Error("pq must in ON_REVIEW state");
      }

      validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.submission2.approval.approvalGroup!);
    } //
    else if (req.index === "3") {
      if (pqTemplate.phasesEvaluation.submission3?.approval?.status !== "ON_REVIEW") {
        throw new Error("pq must in ON_REVIEW state");
      }

      validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.submission3.approval.approvalGroup!);
    } //

    // if (pqTemplate.phasesEvaluation.status !== "ON_REVIEW") {
    //   throw new Error("pq must in ON_REVIEW state");
    // }

    // validateApprovalAction(req.userLogin, pqTemplate.phasesEvaluation.approvalGroup!);

    const docType = ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>;
    const [approvals] = await o.findApprovalGroup(ctx, { documentId: pqTemplate.id, documentType: docType });
    approvals.forEach((apps, i) => {
      apps.approvals.forEach((app) => {
        app.date = null;
        app.signer = null;
        app.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
      });
      apps.status = i === 0 ? "PROCESSING" : "NOT_STARTED";
    });

    await o.saveApprovalGroups(ctx, approvals);

    if (req.index === "1") {
      pqTemplate.phasesEvaluation.submission1!.approval!.status = "DRAFT";
      pqTemplate.phasesEvaluation.submission1!.approval!.isSendBack = true;
      pqTemplate.phasesEvaluation.submission1!.approval!.approvalGroup = approvals[0];
    } //
    else if (req.index === "2") {
      pqTemplate.phasesEvaluation.submission2!.approval!.status = "DRAFT";
      pqTemplate.phasesEvaluation.submission2!.approval!.isSendBack = true;
      pqTemplate.phasesEvaluation.submission2!.approval!.approvalGroup = approvals[0];
    } //
    else if (req.index === "3") {
      pqTemplate.phasesEvaluation.submission3!.approval!.status = "DRAFT";
      pqTemplate.phasesEvaluation.submission3!.approval!.isSendBack = true;
      pqTemplate.phasesEvaluation.submission3!.approval!.approvalGroup = approvals[0];
    }

    await o.savePQTemplate(ctx, pqTemplate);

    if (req.comment === "") {
      throw new Error("comment is required");
    }

    await o.saveDocumentHistory(ctx, {
      documentId: pqTemplate.id!,
      documentType: ("PQ_EVALUATION_" + req.index) as TypeOf<typeof DocumentTemplate>,
      comment: req.comment,
      date: getDateOnly(now),
      message: `PQ Evaluation ${req.index} Sent Back`,
      user: req.userLogin,
      id: `${pqTemplate.id}-${formatDateWithSecond(now)}`,
    });

    return {};
  },
};
