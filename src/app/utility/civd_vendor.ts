import { DataSource } from "typeorm";
import fs from "fs";
import https from "https";
import JSONStream from 'jsonstream';
import path from "path";
import { pipeline } from 'stream';
import { promisify } from 'util';
import { createContext } from "../../framework/core.js";
import { CIVDVendor as ICIVDVendor } from "../gatewayimpl/table_civd_vendor.js";
import { CIVDSpda, CIVDVendor, CIVDVendorBusinessClass, CIVDVendorBusinessLicense, CIVDVendorSaction } from "../model/model_civd_vendor.js";

// const { parser } = Parser;
const AuthToken = "CIVD TIAOWI03-2Q06-V7YU-SNLN-YYTXAZ8L3A";
const CIVDUrl = "https://localhost:9000";
const agent = new https.Agent({
  rejectUnauthorized: false, // Disable SSL certificate validation
});
const pipelineAsync = promisify(pipeline); // To convert pipeline to Promise

const chunkData = (data: any, size: number) => {
  const chunked = [];
  for (let i = 0; i < data.length; i += size) {
    chunked.push(data.slice(i, i + size));
  }
  return chunked;
}

const chunkArray = <T>(array: T[], size: number): T[][] => {
  const chunkedArray: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    const chunk = array.slice(i, i + size);
    chunkedArray.push(chunk);
  }
  return chunkedArray;
};

export const getVendorDataCIVD = async (ds: DataSource) => {
  await getListVendor(ds); // upsert
  await getIzinUsaha(ds); // update
  await getNeraca(ds); // update
  await getSanksiHistory(ds); // update
  await getSpdaHistory(ds); // update

  console.log("getVendorDataCIVD execution done");
}

const getListVendor = async (ds: DataSource) => {
  console.log("executing getListVendor starting");

  const data = await streamReadJsonFile('././docs/civd_vendor/getListVendor.json');

  try {
    const ctx = createContext();
    // Map to promises and await all of them
    const resultArray: CIVDVendor[][] = await Promise.all(
      data.result.map(async (item: any): Promise<CIVDVendor[]> => {

        return [{
          civdVendorId: item.vendorId,
          companyType: item.compType,
          name: item.name,
          npwp: item.npwp,
          address: item.address,
          addressAlternative: item.address2,
          provName: item.provName,
          email: item.vendorEmail1,
          emailAlternative: item.altEmail,
          phone: item.phoneNumber,
          contactPerson: item.contactPerson,
          contactPersonPosition: item.jabatan,
        }];
      })
    );

    // Flatten the array
    const flatResultArray: CIVDVendor[] = resultArray.flat();

    // Chunk the data
    const chunkSize = 100; // Define your chunk size
    const chunks = chunkArray(flatResultArray, chunkSize);

    // Save each chunk to the database
    for (const chunk of chunks) {
      try {
        await ds.getRepository(ICIVDVendor).save(chunk);
        console.log(`Saved chunk of size ${chunk.length} to database.`);
      } catch (error) {
        console.error(`Error saving chunk to database: ${error}`);
      }
    }

    console.log("getListVendor done");
  } catch (error) {
    console.log(`Error during request getListVendor: ${error}`);
  }
}

const getIzinUsaha = async (ds: DataSource) => {
  console.log("executing getIzinUsaha starting");

  // Stream-read the JSON file
  const result = await streamReadJsonFile('././docs/civd_vendor/getIzinUsaha.json');

  try {
    // Map the result data to your expected format
    const resultArray: GetIzinUsaha[] = await Promise.all(
      result.result.map(async (item: any): Promise<any> => ({
        vendorId: item.vendorId,
        companyType: item.compTypeDesc,
        vendorName: item.vendorName,
        businessType: item.jenisIzinUsaha, // jenis usaha
        businessFieldCode: item.bidangUsahaCode,   // kode bidang usaha / KBLI
        businessField: item.bidangUsaha,   // bidang usaha
        businessClass: item.golonganUsaha, // golongan usaha
        businessLicenseNumber: item.noIzinUsaha,
        validityStartDate: item.mulaiBerlaku,
        validityEndDate: item.akhirBerlaku,
      }))
    );

    const groupByVendor = resultArray.reduce((acc: GetIzinUsaha[], current: any) => {
      const vendorId = current.vendorId;

      // Check if the vendor already exists in the accumulator
      let vendorEntry = acc.find(v => v.vendorId === vendorId);

      if (!vendorEntry) {
        // If vendor does not exist, create a new vendor entry
        vendorEntry = {
          vendorId,
          companyType: current.companyType,
          vendorName: current.vendorName,
          info: [],
        };
        acc.push(vendorEntry);
      }

      vendorEntry.info.push({
        businessType: current.businessType,
        businessFieldCode: current.businessFieldCode,
        businessField: current.businessField,
        businessLicenseNumber: current.businessLicenseNumber,
        businessClass: current.businessClass, // golongan usaha
        validityStartDate: current.validityStartDate,
        validityEndDate: current.validityEndDate,
        businessLicenseFile: current.businessLicenseFile || null, // Optional file field
      });

      return acc;
    }, [] as GetIzinUsaha[]);

    // Chunk the data
    const chunkSize = 100; // Define your chunk size
    const chunks = chunkArray(groupByVendor, chunkSize);

    // Save each chunk to the database
    for (const chunk of chunks) {
      try {
        const updatePromises = chunk.map(async (vendor) => {
          if (vendor.vendorId == 19048) {
            console.log(vendor);
          }
          await ds.getRepository(ICIVDVendor).update(
            { civdVendorId: vendor.vendorId }, // Where clause
            { businessLicense: vendor.info } // Data to update
          );
        });

        await Promise.all(updatePromises);

        console.log(`Saved chunk of size ${chunk.length} to database.`);
      } catch (error) {
        console.error(`Error saving chunk to database: ${error}`);
      }
    }

    console.log("Grouped Vendors Count:", groupByVendor.length, "getIzinUsaha done");
  } catch (error) {
    console.log(`Error during request getIzinUsaha: ${error}`);
  }
};

const getNeraca = async (ds: DataSource) => {
  console.log("executing getNeraca starting");

  // Stream-read the JSON file
  const result = await streamReadJsonFile('././docs/civd_vendor/getNeraca.json');

  try {
    // Map the result data to your expected format
    const resultArray: GetNeraca[] = await Promise.all(
      result.result.map(async (item: any): Promise<any> => ({
        vendorId: item.vendorId,
        businessClass: {
          year: item.tahun,
          class: item.golonganPerusahaan
        },
      }))
    );

    const groupByVendor = resultArray.reduce((acc: GetNeraca[], current: any) => {
      const vendorId = current.vendorId;

      // Check if the vendor already exists in the accumulator
      let vendorEntry = acc.find(v => v.vendorId === vendorId);

      if (!vendorEntry) {
        // If vendor does not exist, create a new vendor entry
        vendorEntry = {
          vendorId,
          businessClass: [],
        };
        acc.push(vendorEntry);
      }

      vendorEntry.businessClass.push({
        year: current.businessClass.year,
        class: current.businessClass.class
      });

      return acc;
    }, [] as GetNeraca[]);

    // Chunk the data
    const chunkSize = 100; // Define your chunk size
    const chunks = chunkArray(groupByVendor, chunkSize);

    // Save each chunk to the database
    for (const chunk of chunks) {
      try {
        const updatePromises = chunk.map(async (vendor) => {
          if (vendor.businessClass) {
            await ds.getRepository(ICIVDVendor).update(
              { civdVendorId: vendor.vendorId }, // Where clause
              { businessClass: vendor.businessClass } // Data to update
            );
          }
        });

        await Promise.all(updatePromises);

        console.log(`Saved chunk of size ${chunk.length} to database.`);
      } catch (error) {
        console.error(`Error saving chunk to database: ${error}`);
      }
    }

    console.log("Grouped Vendors Count:", groupByVendor.length, "getNeraca done");
  } catch (error) {
    console.log(`Error during request getNeraca: ${error}`);
  }
}

const getSanksiHistory = async (ds: DataSource) => {
  console.log("executing getSanction starting");

  // Stream-read the JSON file
  const result = await streamReadJsonFile('././docs/civd_vendor/sanksiHistory.json');

  try {
    // Map the result data to your expected format
    const resultArray: GetSanction[] = await Promise.all(
      result.result.map(async (item: any): Promise<any> => ({
        vendorId: item.vendorId,
        sanctionNumber: item.nomorSanksi,
        sanctionColor: item.sanksi,
        description: item.keterangan,
        effectiveDate: item.tanggalBerlakuSanksi,
        endDate: item.tanggalBerakhirSanksi,
        releaseDate: item.tanggalPelepasanSanksi,
        trial: item.percobaan === "Tidak" ? false : true,
        trialEndDate: item.tanggalBerakhirPercobaan
      }))
    );

    const groupByVendor = resultArray.reduce((acc: GetSanction[], current: any) => {
      const vendorId = current.vendorId;

      // Check if the vendor already exists in the accumulator
      let vendorEntry = acc.find(v => v.vendorId === vendorId);

      if (!vendorEntry) {
        // If vendor does not exist, create a new vendor entry
        vendorEntry = {
          vendorId,
          info: [],
        };
        acc.push(vendorEntry);
      }

      vendorEntry.info.push({
        sanctionNumber: current.sanctionNumber,
        sanctionColor: current.sanctionColor,
        description: current.description,
        effectiveDate: current.effectiveDate,
        endDate: current.endDate,
        releaseDate: current.releaseDate,
        trial: current.trial,
        trialEndDate: current.trialEndDate,
      });

      return acc;
    }, [] as GetSanction[]);

    // Chunk the data
    const chunkSize = 100; // Define your chunk size
    const chunks = chunkArray(groupByVendor, chunkSize);

    // Save each chunk to the database
    for (const chunk of chunks) {
      try {
        const updatePromises = chunk.map(async (vendor) => {
          await ds.getRepository(ICIVDVendor).update(
            { civdVendorId: vendor.vendorId }, // Where clause
            { sanction: vendor.info } // Data to update
          );
        });

        await Promise.all(updatePromises);

        console.log(`Saved chunk of size ${chunk.length} to database.`);
      } catch (error) {
        console.error(`Error saving chunk to database: ${error}`);
      }
    }

    console.log("Grouped Vendors Count:", groupByVendor.length, "getSanction done");
  } catch (error) {
    console.log(`Error during request getSanction: ${error}`);
  }
}

const getSpdaHistory = async (ds: DataSource) => {
  console.log("executing getSpda starting");

  // Stream-read the JSON file
  const result = await streamReadJsonFile('././docs/civd_vendor/getSpdaHistory.json');

  try {
    // Map the result data to your expected format
    const resultArray: GetSpda[] = await Promise.all(
      result.result.map(async (item: any): Promise<any> => ({
        vendorId: item.vendorId,
        number: item.spdaNo,
        validity: item.spdaValidity,
        fileId: item.fileSPDAId,
        spdaFile: item.fileSPDA, // civd file url
        uploadDate: item.uploadDate,
        expiredDate: item.expiredDate,
      }))
    );

    const groupByVendor = resultArray.reduce((acc: GetSpda[], current: any) => {
      const vendorId = current.vendorId;

      // Check if the vendor already exists in the accumulator
      let vendorEntry = acc.find(v => v.vendorId === vendorId);

      if (!vendorEntry) {
        // If vendor does not exist, create a new vendor entry
        vendorEntry = {
          vendorId,
          info: [],
        };
        acc.push(vendorEntry);
      }

      vendorEntry.info.push({
        number: current.number,
        validity: current.validity,
        fileId: current.fileId,
        spdaFile: current.spdaFile, // civd file url
        uploadDate: current.uploadDate,
        expiredDate: current.expiredDate,
      });

      return acc;
    }, [] as GetSpda[]);

    // Chunk the data
    const chunkSize = 100; // Define your chunk size
    const chunks = chunkArray(groupByVendor, chunkSize);

    // Save each chunk to the database
    for (const chunk of chunks) {
      try {
        const updatePromises = chunk.map(async (vendor) => {
          await ds.getRepository(ICIVDVendor).update(
            { civdVendorId: vendor.vendorId }, // Where clause
            { spda: vendor.info } // Data to update
          );
        });

        await Promise.all(updatePromises);

        console.log(`Saved chunk of size ${chunk.length} to database.`);
      } catch (error) {
        console.error(`Error saving chunk to database: ${error}`);
      }
    }

    console.log("Grouped Vendors Count:", groupByVendor.length, "getSpda done");
  } catch (error) {
    console.log(`Error during request getSpda: ${error}`);
  }
}

// Stream-read the JSON file and return the data
const streamReadJsonFile = async (filePath: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    // let resultData: any = {};
    const resultData: Record<string, any> = [];

    // Resolve the absolute file path
    const absolutePath = path.resolve(filePath);

    // Create a read stream for the JSON file
    const stream = fs.createReadStream(absolutePath, { encoding: 'utf-8' });

    // Create a JSONStream parser to parse array elements
    const jsonParser = JSONStream.parse(true); // Parses each array element

    // Pipe the file stream to the JSON parser
    pipeline(stream, jsonParser, (err) => {
      if (err) {
        console.error('Pipeline error:', err);
        return reject(err);
      }
    });

    // Listen for data events from the parser and collect the data
    jsonParser.on('data', (data: any) => {
      resultData.push(data);
    });

    // Resolve the promise when parsing is complete
    jsonParser.on('end', () => {
      if (resultData.length === 0) {
        console.log("The file is empty or contains no valid data");
        resolve([]);
      } else {
        // console.log(resultData);
        resolve(resultData[0]);
      }
    });

    // Handle errors during streaming or parsing
    stream.on('error', (err: any) => {
      console.error('File stream error:', err);
      reject(err);
    });

    jsonParser.on('error', (err: any) => {
      console.error('JSONStream parsing error:', err);
      reject(err);
    });
  });
};

type getListVendorFilter = {
  activityName?: string;
  aItEmaiI?: string;
  cityName?: string;
  completedDateFrom?: string; // (yyyy-mm--dd)
  completedDateTo?: string; // (yyyy-mm--dd)
  compType?: string; // Tipe Perusahaan, Filter vendor berdasarkan tipe perusahaan tertentu
  expiredDate?: string; // (yyyy-mm--dd) Masa Berakhir SPDA, Filter berdasarkan tanggal expired SPDA tertentu
  k3sName?: string; // nama KKKS, Filter vendor berdasarkan KKKS tertentu
  name?: string; // Nama vendor, Filter berdasarkan nama vendor tertentu
  npwp?: string;
  participant?: string;
  provName?: string;
  sanksi?: string;
  situ?: string;
  spdaNo?: string;
  vendorEmail1?: string;
  vendorId?: number; // Filter vendor berdasarkan vendor id tertentu
  vendorStatus?: string; // Status Vendor, Filter vendor berdasarkan Status Vendor tertentu, data yang dihasilkan adalah Indonesia dan Foreign
  zipCode?: string;
}

type GetIzinUsaha = {
  vendorId: number;
  companyType: string,
  vendorName: string,
  info: CIVDVendorBusinessLicense[],
};

type GetNeraca = {
  vendorId: number;
  businessClass: CIVDVendorBusinessClass[];
}

type GetSanction = {
  vendorId: number;
  info: CIVDVendorSaction[];
}

type GetSpda = {
  vendorId: number;
  info: CIVDSpda[];
}

let list: getListVendorFilter;

// object vendorCivd
// 1. fetch getlisvendor
// 2. fetch getizinusaha
// 3. fetch getneraca
// 4. fetch sanksihistory
// 5. fetch getSusunanPengurus vendorId
// 6. fetch spdaHistory