import { DataSource } from "typeorm";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import fetch from "node-fetch";
import https from "https";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const API_URL = "https://localhost:9000/vendor/getListVendor";
const AUTH_TOKEN = "CIVD TIAOWI03-2Q06-V7YU-SNLN-YYTXAZ8L3A"; // Replace with actual token
const SAVE_DIR = path.join(__dirname, "docs"); // Change as needed
const FILE_NAME = "vendorList.json"; // Change as needed

const agent = new https.Agent({
  rejectUnauthorized: false, // Disable SSL certificate validation
});

// Function to download and save the response using streaming
export const downloadVendorList = async () => {
  // Ensure download directory exists
  fs.mkdirSync(SAVE_DIR, { recursive: true });

  try {
    console.log("Starting download...");

    // Make the HTTP request with streaming
    const response = await fetch(API_URL, {
      method: "GET",
      headers: {
        Authorization: `${AUTH_TOKEN}`,
      },
      agent,
    });

    if (!response.ok || !response.body) {
      throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
    }

    // Create a writable stream for saving the file
    const filePath = path.join(SAVE_DIR, FILE_NAME);
    const writer = fs.createWriteStream(filePath);

    // Pipe the response stream to the file
    response.body.pipe(writer);

    return new Promise<void>((resolve, reject) => {
      writer.on("finish", () => {
        console.log(`Download completed: ${filePath}`);
        resolve();
      });

      writer.on("error", (err) => {
        console.error("Error writing file", err);
        reject(err);
      });
    });
  } catch (error) {
    console.error("Error fetching vendor list:", error);
  }
};

// Function to download large JSON data from any API URL with streaming and progress monitoring
export const downloadLargeJsonData = async (apiUrl: string, authToken?: string) => {
  const saveDir = path.join(__dirname, "../../../docs/civd_vendor");
  const fileName = "getListVendor.json";
  const filePath = path.join(saveDir, fileName);

  // Ensure download directory exists
  fs.mkdirSync(saveDir, { recursive: true });

  console.log(`Starting download from: ${apiUrl}`);
  console.log(`Saving to: ${filePath}`);

  try {
    const startTime = Date.now();

    // Prepare headers
    const headers: Record<string, string> = {
      'Accept': 'application/json',
      'User-Agent': 'Node.js/Download-Client'
    };

    if (authToken) {
      headers['Authorization'] = authToken;
    }

    // Make the HTTP request with streaming
    const response = await fetch(apiUrl, {
      method: "GET",
      headers,
      agent, // Use the existing HTTPS agent for SSL handling
    });

    if (!response.ok || !response.body) {
      throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
    }

    // Get content length for progress tracking
    const contentLength = response.headers.get('content-length');
    const totalSize = contentLength ? parseInt(contentLength, 10) : null;

    console.log(`Response status: ${response.status}`);
    console.log(`Content-Type: ${response.headers.get('content-type')}`);
    console.log(`Content-Length: ${totalSize ? `${(totalSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}`);

    // Create a writable stream for saving the file
    const writer = fs.createWriteStream(filePath);

    let downloadedBytes = 0;
    let lastLogTime = Date.now();
    const logInterval = 5000; // Log progress every 5 seconds

    // Monitor progress if we have content length
    if (response.body && totalSize) {
      response.body.on('data', (chunk: Buffer) => {
        downloadedBytes += chunk.length;
        const now = Date.now();

        // Log progress every 5 seconds
        if (now - lastLogTime >= logInterval) {
          const progress = ((downloadedBytes / totalSize) * 100).toFixed(2);
          const downloadedMB = (downloadedBytes / 1024 / 1024).toFixed(2);
          const speed = (downloadedBytes / 1024 / 1024) / ((now - startTime) / 1000);

          console.log(`Progress: ${progress}% (${downloadedMB} MB) - Speed: ${speed.toFixed(2)} MB/s`);
          lastLogTime = now;
        }
      });
    }

    // Pipe the response stream to the file
    response.body.pipe(writer);

    return new Promise<void>((resolve, reject) => {
      writer.on("finish", () => {
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        const finalSizeMB = (downloadedBytes / 1024 / 1024).toFixed(2);

        console.log(`✅ Download completed successfully!`);
        console.log(`📁 File saved to: ${filePath}`);
        console.log(`📊 Final size: ${finalSizeMB} MB`);
        console.log(`⏱️  Total time: ${duration.toFixed(2)} seconds`);
        console.log(`🚀 Average speed: ${(downloadedBytes / 1024 / 1024 / duration).toFixed(2)} MB/s`);

        resolve();
      });

      writer.on("error", (err) => {
        console.error("❌ Error writing file:", err);
        reject(err);
      });

      response.body?.on("error", (err) => {
        console.error("❌ Error reading response stream:", err);
        reject(err);
      });
    });

  } catch (error) {
    console.error("❌ Error downloading data:", error);
    throw error;
  }
};